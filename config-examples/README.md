# 运行时配置文件使用说明

## 概述

本项目支持通过运行时配置文件动态设置应用配置，无需重新构建 Docker 镜像即可适配不同环境。

## 配置文件结构

```javascript
window.APP_CONFIG = {
  // 应用基础配置
  APP_TITLE: "应用标题",
  APP_VERSION: "版本号",
  APP_ENV: "环境标识",

  // API配置
  API_BASE_URL: "API基础地址",
  BACKEND_URL: "后端服务器地址", 
  API_TIMEOUT: 超时时间(毫秒),

  // 第三方系统配置
  YINGCHUNHUA_SDK_URL: "迎春花SDK地址",
  YINGCHUNHUA_APP_KEY: "迎春花应用密钥",
  YINGCHUNHUA_APP_SECRET_KEY: "迎春花应用秘钥",

  // 其他配置
  DEBUG_MODE: true/false,
  LOG_LEVEL: "日志级别"
}
```

## 使用方式

### 1. Docker 运行时挂载配置

```bash
# 测试环境
docker run -p 8080:80 \
  -v /path/to/config-examples/config.test.js:/usr/share/nginx/html/config.js \
  your-image-name

# 生产环境
docker run -p 8080:80 \
  -v /path/to/config-examples/config.production.js:/usr/share/nginx/html/config.js \
  your-image-name

# 自定义配置
docker run -p 8080:80 \
  -v /path/to/your/custom-config.js:/usr/share/nginx/html/config.js \
  your-image-name
```

### 2. Docker Compose

```yaml
version: '3.8'
services:
  web:
    image: your-image-name
    ports:
      - "8080:80"
    volumes:
      - ./config-examples/config.test.js:/usr/share/nginx/html/config.js
```

### 3. Kubernetes

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  config.js: |
    window.APP_CONFIG = {
      APP_TITLE: "医院管理系统",
      API_BASE_URL: "https://api.example.com",
      // ... 其他配置
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-app
spec:
  template:
    spec:
      containers:
      - name: web
        image: your-image-name
        volumeMounts:
        - name: config
          mountPath: /usr/share/nginx/html/config.js
          subPath: config.js
      volumes:
      - name: config
        configMap:
          name: app-config
```

## 配置优先级

1. **运行时配置** (`window.APP_CONFIG`) - 最高优先级
2. **环境变量** (`VITE_*`) - 构建时配置
3. **默认值** - 代码中的默认值

## 配置验证

每个配置文件都包含自动验证功能，会在浏览器控制台输出验证结果：

- ✅ 配置验证通过
- ❌ 配置验证失败: [字段名] 不能为空

## 注意事项

1. **配置文件路径**：必须挂载到 `/usr/share/nginx/html/config.js`
2. **配置格式**：必须是有效的 JavaScript 文件
3. **必填字段**：`APP_TITLE`、`API_BASE_URL`、`BACKEND_URL`
4. **安全性**：生产环境中请确保配置文件的安全性

## 示例配置文件

- `config.test.js` - 测试环境配置
- `config.production.js` - 生产环境配置
- `../public/config.js` - 默认配置模板

## 故障排除

### 配置未生效
1. 检查配置文件是否正确挂载
2. 检查配置文件语法是否正确
3. 查看浏览器控制台是否有错误信息

### API 请求失败
1. 检查 `API_BASE_URL` 和 `BACKEND_URL` 是否正确
2. 检查网络连接和 CORS 配置
3. 检查后端服务是否正常运行

### 第三方集成失败
1. 检查第三方系统配置是否正确
2. 检查第三方服务是否可访问
3. 检查密钥和秘钥是否有效
