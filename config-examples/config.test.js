/**
 * 测试环境配置文件
 * 
 * 使用方式：
 * docker run -p 8080:80 \
 *   -v /path/to/config-examples/config.test.js:/usr/share/nginx/html/config.js \
 *   your-image-name
 */
window.APP_CONFIG = {
  // 应用基础配置
  APP_TITLE: "测试医院管理系统(测试)",
  APP_VERSION: "1.0.0-test",
  APP_ENV: "test",

  // API配置 - 测试环境
  API_BASE_URL: "http://8.140.53.70:9090",
  BACKEND_URL: "http://8.140.53.70:9090", 
  API_TIMEOUT: 12000,

  // 第三方系统配置 - 测试环境
  YINGCHUNHUA_SDK_URL: "http://183.242.68.188:32103/client_app_iframe/index.js",
  YINGCHUNHUA_APP_KEY: "ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09",
  YINGCHUNHUA_APP_SECRET_KEY: "YYCloud1644286723584",

  // 其他配置
  DEBUG_MODE: true,
  LOG_LEVEL: "debug"
}

// 配置验证函数
window.validateConfig = function() {
  const config = window.APP_CONFIG
  const requiredFields = ['APP_TITLE', 'API_BASE_URL', 'BACKEND_URL']
  
  for (const field of requiredFields) {
    if (!config[field]) {
      console.error(`❌ 配置验证失败: ${field} 不能为空`)
      return false
    }
  }
  
  console.log('✅ 测试环境配置验证通过')
  return true
}

// 自动验证配置
if (typeof window !== 'undefined') {
  window.validateConfig()
  console.log('🔧 测试环境配置已加载:', window.APP_CONFIG)
}
