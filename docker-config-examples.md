# Docker 运行时配置使用示例

## 概述

本文档提供了在 Docker 环境中使用运行时配置的完整示例，包括不同部署场景的配置方法。

## 基础使用

### 1. 使用测试环境配置

```bash
# 拉取或构建镜像
docker build -t yyhis-web .

# 使用测试环境配置运行
docker run -d \
  --name yyhis-web-test \
  -p 8080:80 \
  -v $(pwd)/config-examples/config.test.js:/usr/share/nginx/html/config.js \
  yyhis-web

# 访问应用
curl http://localhost:8080
```

### 2. 使用生产环境配置

```bash
# 使用生产环境配置运行
docker run -d \
  --name yyhis-web-prod \
  -p 8080:80 \
  -v $(pwd)/config-examples/config.production.js:/usr/share/nginx/html/config.js \
  yyhis-web
```

### 3. 使用自定义配置

```bash
# 创建自定义配置文件
cat > my-config.js << 'EOF'
window.APP_CONFIG = {
  APP_TITLE: "我的医院管理系统",
  APP_VERSION: "1.0.0",
  APP_ENV: "custom",
  API_BASE_URL: "https://my-api.example.com",
  BACKEND_URL: "https://my-api.example.com",
  API_TIMEOUT: 20000,
  YINGCHUNHUA_SDK_URL: "http://**************:8094/client_app_iframe/index.js",
  YINGCHUNHUA_APP_KEY: "your-app-key",
  YINGCHUNHUA_APP_SECRET_KEY: "your-secret-key",
  DEBUG_MODE: false,
  LOG_LEVEL: "info"
}
EOF

# 使用自定义配置运行
docker run -d \
  --name yyhis-web-custom \
  -p 8080:80 \
  -v $(pwd)/my-config.js:/usr/share/nginx/html/config.js \
  yyhis-web
```

## Docker Compose 部署

### 1. 基础 docker-compose.yml

```yaml
version: '3.8'

services:
  web:
    image: yyhis-web:latest
    container_name: yyhis-web
    ports:
      - "8080:80"
    volumes:
      - ./config-examples/config.test.js:/usr/share/nginx/html/config.js
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
    networks:
      - yyhis-network

networks:
  yyhis-network:
    driver: bridge
```

### 2. 多环境 docker-compose

```yaml
# docker-compose.test.yml
version: '3.8'

services:
  web-test:
    image: yyhis-web:latest
    container_name: yyhis-web-test
    ports:
      - "8080:80"
    volumes:
      - ./config-examples/config.test.js:/usr/share/nginx/html/config.js
    restart: unless-stopped

---
# docker-compose.prod.yml
version: '3.8'

services:
  web-prod:
    image: yyhis-web:latest
    container_name: yyhis-web-prod
    ports:
      - "80:80"
    volumes:
      - ./config-examples/config.production.js:/usr/share/nginx/html/config.js
    restart: unless-stopped
```

### 3. 使用环境变量的 docker-compose

```yaml
version: '3.8'

services:
  web:
    image: yyhis-web:latest
    container_name: yyhis-web-${ENV:-test}
    ports:
      - "${PORT:-8080}:80"
    volumes:
      - ./config-examples/config.${ENV:-test}.js:/usr/share/nginx/html/config.js
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
```

使用方式：
```bash
# 测试环境
ENV=test PORT=8080 docker-compose up -d

# 生产环境
ENV=production PORT=80 docker-compose up -d
```

## Kubernetes 部署

### 1. ConfigMap 配置

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: yyhis-web-config
  namespace: default
data:
  config.js: |
    window.APP_CONFIG = {
      APP_TITLE: "医院管理系统",
      APP_VERSION: "1.0.0",
      APP_ENV: "production",
      API_BASE_URL: "https://api.yyhospital.com",
      BACKEND_URL: "https://api.yyhospital.com",
      API_TIMEOUT: 15000,
      YINGCHUNHUA_SDK_URL: "http://**************:8094/client_app_iframe/index.js",
      YINGCHUNHUA_APP_KEY: "ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09",
      YINGCHUNHUA_APP_SECRET_KEY: "YYCloud1644286723584",
      DEBUG_MODE: false,
      LOG_LEVEL: "error"
    }
```

### 2. Deployment 配置

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yyhis-web
  namespace: default
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yyhis-web
  template:
    metadata:
      labels:
        app: yyhis-web
    spec:
      containers:
      - name: web
        image: yyhis-web:latest
        ports:
        - containerPort: 80
        volumeMounts:
        - name: config
          mountPath: /usr/share/nginx/html/config.js
          subPath: config.js
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: config
        configMap:
          name: yyhis-web-config
```

### 3. Service 配置

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: yyhis-web-service
  namespace: default
spec:
  selector:
    app: yyhis-web
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
```

## 配置验证和调试

### 1. 验证配置是否生效

```bash
# 检查容器中的配置文件
docker exec yyhis-web-test cat /usr/share/nginx/html/config.js

# 检查应用是否正确加载配置
docker exec yyhis-web-test curl -s http://localhost/config.js
```

### 2. 查看应用日志

```bash
# 查看容器日志
docker logs yyhis-web-test

# 实时查看日志
docker logs -f yyhis-web-test
```

### 3. 浏览器调试

打开浏览器开发者工具，在控制台中检查：

```javascript
// 检查配置是否加载
console.log(window.APP_CONFIG)

// 检查配置验证结果
window.validateConfig()
```

## 常见问题和解决方案

### 1. 配置文件未生效

**问题**：应用仍然使用默认配置

**解决方案**：
```bash
# 检查文件挂载是否正确
docker exec container_name ls -la /usr/share/nginx/html/config.js

# 检查文件内容
docker exec container_name cat /usr/share/nginx/html/config.js

# 重启容器
docker restart container_name
```

### 2. API 请求失败

**问题**：前端无法连接到后端 API

**解决方案**：
```bash
# 检查网络连通性
docker exec container_name ping api-server

# 检查配置中的 API 地址
docker exec container_name grep -E "(API_BASE_URL|BACKEND_URL)" /usr/share/nginx/html/config.js
```

### 3. 配置语法错误

**问题**：JavaScript 语法错误导致配置无法加载

**解决方案**：
```bash
# 验证 JavaScript 语法
node -c config.js

# 或使用在线工具验证 JSON 格式
```

## 自动化部署脚本

### 1. 部署脚本示例

```bash
#!/bin/bash
# deploy.sh

set -e

ENV=${1:-test}
PORT=${2:-8080}
IMAGE_TAG=${3:-latest}

echo "🚀 开始部署 yyhis-web (环境: $ENV, 端口: $PORT)"

# 停止并删除现有容器
docker stop yyhis-web-$ENV 2>/dev/null || true
docker rm yyhis-web-$ENV 2>/dev/null || true

# 启动新容器
docker run -d \
  --name yyhis-web-$ENV \
  -p $PORT:80 \
  -v $(pwd)/config-examples/config.$ENV.js:/usr/share/nginx/html/config.js \
  --restart unless-stopped \
  yyhis-web:$IMAGE_TAG

echo "✅ 部署完成！"
echo "🌐 访问地址: http://localhost:$PORT"

# 等待服务启动
sleep 5

# 健康检查
if curl -f http://localhost:$PORT >/dev/null 2>&1; then
  echo "✅ 健康检查通过"
else
  echo "❌ 健康检查失败"
  exit 1
fi
```

使用方式：
```bash
# 部署测试环境
./deploy.sh test 8080

# 部署生产环境
./deploy.sh production 80
```

### 2. 配置更新脚本

```bash
#!/bin/bash
# update-config.sh

CONTAINER_NAME=${1:-yyhis-web-test}
CONFIG_FILE=${2:-config-examples/config.test.js}

echo "🔄 更新配置文件: $CONFIG_FILE -> $CONTAINER_NAME"

# 验证配置文件语法
if ! node -c "$CONFIG_FILE"; then
  echo "❌ 配置文件语法错误"
  exit 1
fi

# 备份当前配置
docker exec $CONTAINER_NAME cp /usr/share/nginx/html/config.js /usr/share/nginx/html/config.js.backup

# 更新配置文件
docker cp "$CONFIG_FILE" $CONTAINER_NAME:/usr/share/nginx/html/config.js

# 重启 nginx（如果需要）
docker exec $CONTAINER_NAME nginx -s reload

echo "✅ 配置更新完成"
```

## 监控和日志

### 1. 配置监控

```bash
# 监控配置文件变化
watch -n 5 'docker exec yyhis-web-test cat /usr/share/nginx/html/config.js | grep APP_TITLE'

# 监控应用状态
watch -n 10 'curl -s http://localhost:8080 | grep -o "<title>[^<]*"'
```

### 2. 日志收集

```yaml
# docker-compose.yml with logging
version: '3.8'

services:
  web:
    image: yyhis-web:latest
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    volumes:
      - ./config-examples/config.test.js:/usr/share/nginx/html/config.js
      - ./logs:/var/log/nginx
```

这样就完成了运行时配置系统的实现！现在您可以：

1. **构建一次镜像**，在不同环境中使用不同的配置文件
2. **通过 Docker 挂载**动态替换配置，无需重新构建
3. **支持多种部署方式**：Docker、Docker Compose、Kubernetes
4. **配置验证**：自动验证配置的完整性和正确性
5. **灵活配置**：支持应用标题、API地址、第三方集成等所有配置项
