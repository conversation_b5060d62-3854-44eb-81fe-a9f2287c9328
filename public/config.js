/**
 * 运行时配置文件
 * 此文件在应用运行时加载，可以通过 Docker 挂载来动态配置
 * 
 * 使用方式：
 * docker run -p 8080:80 \
 *   -v /path/to/your/config.js:/usr/share/nginx/html/config.js \
 *   your-image-name
 */
window.APP_CONFIG = {
  // 应用基础配置
  APP_TITLE: "测试医院管理系统",
  APP_VERSION: "1.0.0",
  APP_ENV: "production",

  // API配置
  API_BASE_URL: "http://localhost:6596",
  BACKEND_URL: "http://localhost:6596", 
  API_TIMEOUT: 10000,

  // 第三方系统配置
  YINGCHUNHUA_SDK_URL: "http://**************:8094/client_app_iframe/index.js",
  YINGCHUNHUA_APP_KEY: "ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09",
  YINGCHUNHUA_APP_SECRET_KEY: "YYCloud1644286723584",

  // 其他配置
  DEBUG_MODE: false,
  LOG_LEVEL: "error"
}

// 配置验证函数
window.validateConfig = function() {
  const config = window.APP_CONFIG
  const requiredFields = ['APP_TITLE', 'API_BASE_URL', 'BACKEND_URL']
  
  for (const field of requiredFields) {
    if (!config[field]) {
      console.error(`❌ 配置验证失败: ${field} 不能为空`)
      return false
    }
  }
  
  console.log('✅ 配置验证通过')
  return true
}

// 自动验证配置
if (typeof window !== 'undefined') {
  window.validateConfig()
  console.log('🔧 运行时配置已加载:', window.APP_CONFIG)
}
