# 运行时配置使用指南

## 概述

本项目已实现运行时配置系统，允许您在 Docker 运行时通过挂载不同的配置文件来指定环境配置，无需重新构建镜像。

## 🎯 核心特性

- ✅ **运行时配置**：Docker 运行时动态替换配置
- ✅ **配置优先级**：运行时配置 > 环境变量 > 默认值
- ✅ **配置验证**：自动验证配置完整性和格式
- ✅ **多环境支持**：测试、生产等不同环境配置
- ✅ **热更新**：无需重启应用即可更新配置
- ✅ **类型安全**：配置字段类型验证

## 📁 文件结构

```
├── public/
│   └── config.js                    # 默认运行时配置文件
├── config-examples/
│   ├── config.test.js              # 测试环境配置示例
│   ├── config.production.js        # 生产环境配置示例
│   └── README.md                   # 配置文件说明
├── scripts/
│   └── validate-runtime-config.js  # 配置验证脚本
├── docker-config-examples.md       # Docker 使用示例
└── 运行时配置使用指南.md           # 本文档
```

## 🚀 快速开始

### 1. 构建镜像

```bash
# 构建 Docker 镜像
docker build -t yyhis-web .
```

### 2. 使用测试环境配置运行

```bash
docker run -d \
  --name yyhis-web-test \
  -p 8080:80 \
  -v $(pwd)/config-examples/config.test.js:/usr/share/nginx/html/config.js \
  yyhis-web
```

### 3. 访问应用

```bash
# 打开浏览器访问
open http://localhost:8080

# 或使用 curl 测试
curl http://localhost:8080
```

### 4. 验证配置

在浏览器开发者工具控制台中：

```javascript
// 查看当前配置
console.log(window.APP_CONFIG)

// 验证配置
window.validateConfig()
```

## ⚙️ 配置文件格式

### 基础结构

```javascript
window.APP_CONFIG = {
  // 应用基础配置
  APP_TITLE: "应用标题",
  APP_VERSION: "1.0.0",
  APP_ENV: "production",

  // API配置
  API_BASE_URL: "https://api.example.com",
  BACKEND_URL: "https://api.example.com", 
  API_TIMEOUT: 15000,

  // 第三方系统配置
  YINGCHUNHUA_SDK_URL: "http://example.com/sdk.js",
  YINGCHUNHUA_APP_KEY: "your-app-key",
  YINGCHUNHUA_APP_SECRET_KEY: "your-secret-key",

  // 其他配置
  DEBUG_MODE: false,
  LOG_LEVEL: "error"
}
```

### 配置字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `APP_TITLE` | string | ✅ | 应用标题，显示在浏览器标题栏 |
| `APP_VERSION` | string | ✅ | 应用版本号 |
| `APP_ENV` | string | ✅ | 环境标识：development/test/production |
| `API_BASE_URL` | string | ✅ | API 基础地址 |
| `BACKEND_URL` | string | ✅ | 后端服务器地址 |
| `API_TIMEOUT` | number | ✅ | API 请求超时时间（毫秒） |
| `YINGCHUNHUA_SDK_URL` | string | ❌ | 迎春花 SDK 地址 |
| `YINGCHUNHUA_APP_KEY` | string | ❌ | 迎春花应用密钥 |
| `YINGCHUNHUA_APP_SECRET_KEY` | string | ❌ | 迎春花应用秘钥 |
| `DEBUG_MODE` | boolean | ❌ | 调试模式开关 |
| `LOG_LEVEL` | string | ❌ | 日志级别：debug/info/warn/error |

## 🔧 配置验证

### 使用验证脚本

```bash
# 验证所有配置文件
npm run config:validate

# 验证特定配置文件
npm run config:validate:test
npm run config:validate:prod

# 验证自定义配置文件
node scripts/validate-runtime-config.js /path/to/your/config.js
```

### 验证输出示例

```
🔍 验证配置文件: config-examples/config.test.js
==================================================
📝 验证 JavaScript 语法...
✅ JavaScript 语法验证通过

📋 验证配置完整性...

必需字段检查:
  ✅ APP_TITLE: 测试医院管理系统(测试)
  ✅ APP_VERSION: 1.0.0-test
  ✅ APP_ENV: test
  ✅ API_BASE_URL: http://***********:9090
  ✅ BACKEND_URL: http://***********:9090
  ✅ API_TIMEOUT: 12000

🔍 字段格式验证...
  ✅ API_BASE_URL: URL 格式正确
  ✅ BACKEND_URL: URL 格式正确
  ✅ API_TIMEOUT: 12000ms
  ✅ APP_ENV: test

✅ 配置验证通过！
```

## 🐳 Docker 部署

### 单容器部署

```bash
# 测试环境
docker run -d \
  --name yyhis-web-test \
  -p 8080:80 \
  -v $(pwd)/config-examples/config.test.js:/usr/share/nginx/html/config.js \
  yyhis-web

# 生产环境
docker run -d \
  --name yyhis-web-prod \
  -p 80:80 \
  -v $(pwd)/config-examples/config.production.js:/usr/share/nginx/html/config.js \
  yyhis-web
```

### Docker Compose 部署

```yaml
version: '3.8'

services:
  web:
    image: yyhis-web:latest
    container_name: yyhis-web
    ports:
      - "8080:80"
    volumes:
      - ./config-examples/config.test.js:/usr/share/nginx/html/config.js
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
```

### Kubernetes 部署

```yaml
# ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: yyhis-web-config
data:
  config.js: |
    window.APP_CONFIG = {
      APP_TITLE: "医院管理系统",
      API_BASE_URL: "https://api.yyhospital.com",
      // ... 其他配置
    }

---
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yyhis-web
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yyhis-web
  template:
    metadata:
      labels:
        app: yyhis-web
    spec:
      containers:
      - name: web
        image: yyhis-web:latest
        ports:
        - containerPort: 80
        volumeMounts:
        - name: config
          mountPath: /usr/share/nginx/html/config.js
          subPath: config.js
      volumes:
      - name: config
        configMap:
          name: yyhis-web-config
```

## 🔄 配置更新

### 热更新配置

```bash
# 更新配置文件
docker cp new-config.js container_name:/usr/share/nginx/html/config.js

# 刷新浏览器页面即可生效
```

### 批量更新

```bash
#!/bin/bash
# 批量更新多个容器的配置

CONTAINERS=("yyhis-web-test" "yyhis-web-staging")
CONFIG_FILE="config-examples/config.test.js"

for container in "${CONTAINERS[@]}"; do
  echo "更新容器 $container 的配置..."
  docker cp "$CONFIG_FILE" "$container:/usr/share/nginx/html/config.js"
  echo "✅ $container 配置更新完成"
done
```

## 🐛 故障排除

### 1. 配置未生效

**症状**：应用仍然使用默认配置

**排查步骤**：
```bash
# 1. 检查文件是否正确挂载
docker exec container_name ls -la /usr/share/nginx/html/config.js

# 2. 检查文件内容
docker exec container_name cat /usr/share/nginx/html/config.js

# 3. 检查浏览器控制台
# 打开开发者工具，查看是否有 JavaScript 错误

# 4. 验证配置语法
node scripts/validate-runtime-config.js your-config.js
```

### 2. API 请求失败

**症状**：前端无法连接后端

**排查步骤**：
```bash
# 1. 检查 API 地址配置
docker exec container_name grep -E "(API_BASE_URL|BACKEND_URL)" /usr/share/nginx/html/config.js

# 2. 测试网络连通性
docker exec container_name ping your-api-server

# 3. 检查 CORS 配置
curl -H "Origin: http://localhost:8080" -v http://your-api-server/api/test
```

### 3. 第三方集成失败

**症状**：第三方系统无法正常工作

**排查步骤**：
```bash
# 1. 检查第三方配置
docker exec container_name grep -E "YINGCHUNHUA" /usr/share/nginx/html/config.js

# 2. 测试第三方服务可访问性
curl -v http://**************:8094/client_app_iframe/index.js

# 3. 检查密钥配置
# 确保 APP_KEY 和 APP_SECRET_KEY 正确
```

## 📊 监控和日志

### 配置监控

```bash
# 监控配置变化
watch -n 5 'docker exec yyhis-web-test cat /usr/share/nginx/html/config.js | grep APP_TITLE'

# 监控应用状态
watch -n 10 'curl -s http://localhost:8080 | grep -o "<title>[^<]*"'
```

### 日志分析

```bash
# 查看应用日志
docker logs yyhis-web-test

# 实时监控日志
docker logs -f yyhis-web-test

# 过滤配置相关日志
docker logs yyhis-web-test 2>&1 | grep -i config
```

## 🔒 安全注意事项

1. **敏感信息保护**：不要在配置文件中明文存储密码等敏感信息
2. **文件权限**：确保配置文件具有适当的读取权限
3. **网络安全**：在生产环境中使用 HTTPS
4. **访问控制**：限制对配置文件的访问权限

## 📚 相关文档

- [Docker 配置示例](./docker-config-examples.md)
- [配置文件说明](./config-examples/README.md)
- [部署文档](./部署文档.md)

## 🆘 获取帮助

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 运行配置验证脚本
3. 检查浏览器开发者工具的控制台
4. 查看 Docker 容器日志

---

**恭喜！** 您已经成功配置了运行时配置系统。现在可以在不重新构建镜像的情况下，灵活地在不同环境中部署应用了！ 🎉
