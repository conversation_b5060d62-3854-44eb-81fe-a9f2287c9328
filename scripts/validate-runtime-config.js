#!/usr/bin/env node

/**
 * 运行时配置验证脚本
 * 用于验证配置文件的语法和完整性
 */

import { readFileSync, existsSync } from 'fs'
import { resolve } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const root = resolve(__dirname, '..')

// 必需的配置字段
const REQUIRED_FIELDS = [
  'APP_TITLE',
  'APP_VERSION', 
  'APP_ENV',
  'API_BASE_URL',
  'BACKEND_URL',
  'API_TIMEOUT'
]

// 可选的配置字段
const OPTIONAL_FIELDS = [
  'YINGCHUNHUA_SDK_URL',
  'YINGCHUNHUA_APP_KEY',
  'YINGCHUNHUA_APP_SECRET_KEY',
  'DEBUG_MODE',
  'LOG_LEVEL'
]

/**
 * 验证配置文件
 * @param {string} configPath - 配置文件路径
 * @returns {boolean} 验证是否通过
 */
function validateConfigFile(configPath) {
  console.log(`\n🔍 验证配置文件: ${configPath}`)
  console.log('=' .repeat(50))

  // 检查文件是否存在
  if (!existsSync(configPath)) {
    console.error(`❌ 配置文件不存在: ${configPath}`)
    return false
  }

  try {
    // 读取文件内容
    const content = readFileSync(configPath, 'utf8')
    
    // 验证 JavaScript 语法
    console.log('📝 验证 JavaScript 语法...')
    
    // 创建一个安全的执行环境
    const mockWindow = {}
    const mockConsole = {
      log: () => {},
      error: () => {},
      group: () => {},
      groupEnd: () => {}
    }
    
    // 执行配置文件
    const func = new Function('window', 'console', content)
    func(mockWindow, mockConsole)
    
    if (!mockWindow.APP_CONFIG) {
      console.error('❌ 配置文件中未找到 window.APP_CONFIG')
      return false
    }
    
    console.log('✅ JavaScript 语法验证通过')
    
    // 验证配置完整性
    console.log('\n📋 验证配置完整性...')
    
    const config = mockWindow.APP_CONFIG
    let isValid = true
    
    // 检查必需字段
    console.log('\n必需字段检查:')
    for (const field of REQUIRED_FIELDS) {
      const value = config[field]
      const hasValue = value !== undefined && value !== null && value !== ''
      
      console.log(`  ${hasValue ? '✅' : '❌'} ${field}: ${hasValue ? value : '(缺失)'}`)
      
      if (!hasValue) {
        isValid = false
      }
    }
    
    // 检查可选字段
    console.log('\n可选字段检查:')
    for (const field of OPTIONAL_FIELDS) {
      const value = config[field]
      const hasValue = value !== undefined && value !== null && value !== ''
      
      console.log(`  ${hasValue ? '✅' : '⚠️ '} ${field}: ${hasValue ? value : '(未设置)'}`)
    }
    
    // 验证特定字段格式
    console.log('\n🔍 字段格式验证...')
    
    // 验证 URL 格式
    const urlFields = ['API_BASE_URL', 'BACKEND_URL', 'YINGCHUNHUA_SDK_URL']
    for (const field of urlFields) {
      const value = config[field]
      if (value) {
        try {
          new URL(value)
          console.log(`  ✅ ${field}: URL 格式正确`)
        } catch (error) {
          console.log(`  ❌ ${field}: URL 格式错误 - ${value}`)
          isValid = false
        }
      }
    }
    
    // 验证超时时间
    if (config.API_TIMEOUT) {
      const timeout = parseInt(config.API_TIMEOUT)
      if (isNaN(timeout) || timeout <= 0) {
        console.log(`  ❌ API_TIMEOUT: 必须是正整数 - ${config.API_TIMEOUT}`)
        isValid = false
      } else {
        console.log(`  ✅ API_TIMEOUT: ${timeout}ms`)
      }
    }
    
    // 验证环境类型
    if (config.APP_ENV) {
      const validEnvs = ['development', 'test', 'production']
      if (validEnvs.includes(config.APP_ENV)) {
        console.log(`  ✅ APP_ENV: ${config.APP_ENV}`)
      } else {
        console.log(`  ⚠️  APP_ENV: 非标准环境类型 - ${config.APP_ENV}`)
      }
    }
    
    // 验证布尔值字段
    const booleanFields = ['DEBUG_MODE']
    for (const field of booleanFields) {
      const value = config[field]
      if (value !== undefined) {
        if (typeof value === 'boolean') {
          console.log(`  ✅ ${field}: ${value}`)
        } else {
          console.log(`  ⚠️  ${field}: 应该是布尔值 - ${value}`)
        }
      }
    }
    
    // 显示配置摘要
    console.log('\n📊 配置摘要:')
    console.log(`  应用标题: ${config.APP_TITLE}`)
    console.log(`  应用版本: ${config.APP_VERSION}`)
    console.log(`  运行环境: ${config.APP_ENV}`)
    console.log(`  API地址: ${config.API_BASE_URL}`)
    console.log(`  后端地址: ${config.BACKEND_URL}`)
    console.log(`  超时时间: ${config.API_TIMEOUT}ms`)
    
    if (isValid) {
      console.log('\n✅ 配置验证通过！')
    } else {
      console.log('\n❌ 配置验证失败！请修复上述问题。')
    }
    
    return isValid
    
  } catch (error) {
    console.error(`❌ 配置文件语法错误: ${error.message}`)
    return false
  }
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2)
  
  console.log('🔧 运行时配置验证工具')
  console.log('=' .repeat(50))
  
  if (args.length === 0) {
    // 验证所有示例配置文件
    const configFiles = [
      'public/config.js',
      'config-examples/config.test.js',
      'config-examples/config.production.js'
    ]
    
    let allValid = true
    
    for (const configFile of configFiles) {
      const configPath = resolve(root, configFile)
      const isValid = validateConfigFile(configPath)
      if (!isValid) {
        allValid = false
      }
    }
    
    console.log('\n' + '=' .repeat(50))
    if (allValid) {
      console.log('🎉 所有配置文件验证通过！')
      process.exit(0)
    } else {
      console.log('💥 部分配置文件验证失败！')
      process.exit(1)
    }
  } else {
    // 验证指定的配置文件
    const configPath = resolve(process.cwd(), args[0])
    const isValid = validateConfigFile(configPath)
    
    console.log('\n' + '=' .repeat(50))
    if (isValid) {
      console.log('🎉 配置文件验证通过！')
      process.exit(0)
    } else {
      console.log('💥 配置文件验证失败！')
      process.exit(1)
    }
  }
}

// 运行主函数
main()
