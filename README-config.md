# 第三方系统运行时配置

## 简介

可以通过 Docker 挂载配置文件来动态设置第三方系统配置，无需重新构建镜像。

## 配置文件

`public/config.js` - 第三方系统配置文件：

```javascript
window.APP_CONFIG = {
  // 第三方系统配置
  YINGCHUNHUA_SDK_URL: "http://**************:8094/client_app_iframe/index.js",
  YINGCHUNHUA_APP_KEY: "ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09",
  YINGCHUNHUA_APP_SECRET_KEY: "YYCloud1644286723584"
}
```

## 使用方法

### 1. 创建自定义配置文件

```bash
# 创建你的配置文件
cat > my-config.js << 'EOF'
window.APP_CONFIG = {
  YINGCHUNHUA_SDK_URL: "你的SDK地址",
  YINGCHUNHUA_APP_KEY: "你的APP_KEY",
  YINGCHUNHUA_APP_SECRET_KEY: "你的SECRET_KEY"
}
EOF
```

### 2. Docker 运行时挂载配置

```bash
# 构建镜像
docker build -t yyhis-web .

# 使用自定义配置运行
docker run -p 8080:80 \
  -v $(pwd)/my-config.js:/usr/share/nginx/html/config.js \
  yyhis-web
```

### 3. 验证配置

打开浏览器访问 http://localhost:8080，在开发者工具控制台中查看：

```javascript
// 查看当前配置
console.log(window.APP_CONFIG)
```

## 注意事项

1. 配置文件必须挂载到 `/usr/share/nginx/html/config.js`
2. 配置文件必须是有效的 JavaScript 格式
3. 如果不挂载配置文件，将使用默认配置

就这么简单！🎉
